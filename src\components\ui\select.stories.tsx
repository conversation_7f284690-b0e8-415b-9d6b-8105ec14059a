import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './select';

const meta: Meta<typeof Select> = {
  title: 'UI/Select',
  component: Select,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified Select component that supports both single and multi-selection functionality. Built on top of Radix UI Select with enhanced multi-select capabilities.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    multiSelect: {
      control: 'boolean',
      description: 'Enable multi-select mode',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
    },
    maxSelected: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Maximum number of items that can be selected (multi-select only)',
      if: { arg: 'multiSelect', eq: true },
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text when no items are selected',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data
const frameworks = [
  { value: 'next.js', label: 'Next.js' },
  { value: 'react', label: 'React' },
  { value: 'vue', label: 'Vue.js' },
  { value: 'angular', label: 'Angular' },
  { value: 'svelte', label: 'Svelte' },
  { value: 'solid', label: 'SolidJS' },
];

const timezones = [
  { value: 'est', label: 'Eastern Standard Time' },
  { value: 'cst', label: 'Central Standard Time' },
  { value: 'mst', label: 'Mountain Standard Time' },
  { value: 'pst', label: 'Pacific Standard Time' },
  { value: 'gmt', label: 'Greenwich Mean Time' },
  { value: 'cet', label: 'Central European Time' },
];

// Basic single select story
export const SingleSelect: Story = {
  render: () => {
    const [value, setValue] = useState<string>('');
    
    return (
      <div className="w-[280px]">
        <Select value={value} onValueChange={setValue}>
          <SelectTrigger>
            <SelectValue placeholder="Select a framework" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Frameworks</SelectLabel>
              {frameworks.map((framework) => (
                <SelectItem key={framework.value} value={framework.value}>
                  {framework.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        {value && (
          <p className="mt-2 text-sm text-muted-foreground">
            Selected: <span className="font-medium">{frameworks.find(f => f.value === value)?.label}</span>
          </p>
        )}
      </div>
    );
  },
};

// Multi-select story
export const MultiSelect: Story = {
  render: () => {
    const [values, setValues] = useState<string[]>([]);
    
    return (
      <div className="w-[320px]">
        <Select 
          multiSelect 
          value={values} 
          onValueChange={setValues}
          placeholder="Select multiple timezones"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Timezones</SelectLabel>
              {timezones.map((timezone) => (
                <SelectItem key={timezone.value} value={timezone.value}>
                  {timezone.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        {values.length > 0 && (
          <div className="mt-2 text-sm text-muted-foreground">
            <p>Selected ({values.length}):</p>
            <ul className="list-disc list-inside">
              {values.map(value => (
                <li key={value}>{timezones.find(t => t.value === value)?.label}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  },
};

// Multi-select with max limit
export const MultiSelectWithLimit: Story = {
  render: () => {
    const [values, setValues] = useState<string[]>([]);
    const maxSelected = 3;
    
    return (
      <div className="w-[320px]">
        <Select 
          multiSelect 
          value={values} 
          onValueChange={setValues}
          maxSelected={maxSelected}
          placeholder="Select up to 3 frameworks"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Frameworks (Max {maxSelected})</SelectLabel>
              {frameworks.map((framework) => (
                <SelectItem key={framework.value} value={framework.value}>
                  {framework.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        <div className="mt-2 text-sm text-muted-foreground">
          <p>{values.length}/{maxSelected} selected</p>
          {values.length >= maxSelected && (
            <p className="text-amber-600">Maximum selection reached</p>
          )}
        </div>
      </div>
    );
  },
};

// Comparison story showing both modes
export const Comparison: Story = {
  render: () => {
    const [singleValue, setSingleValue] = useState<string>('');
    const [multiValues, setMultiValues] = useState<string[]>([]);
    
    return (
      <div className="grid gap-8 w-full max-w-2xl">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Single Select</h3>
          <p className="text-sm text-muted-foreground">
            Traditional select behavior - choose one option
          </p>
          <Select value={singleValue} onValueChange={setSingleValue}>
            <SelectTrigger className="w-[280px]">
              <SelectValue placeholder="Select a framework" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Frameworks</SelectLabel>
                {frameworks.map((framework) => (
                  <SelectItem key={framework.value} value={framework.value}>
                    {framework.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Multi Select</h3>
          <p className="text-sm text-muted-foreground">
            Enhanced behavior - choose multiple options with badges
          </p>
          <Select 
            multiSelect 
            value={multiValues} 
            onValueChange={setMultiValues}
            placeholder="Select multiple frameworks"
          >
            <SelectTrigger className="w-[320px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Frameworks</SelectLabel>
                {frameworks.map((framework) => (
                  <SelectItem key={framework.value} value={framework.value}>
                    {framework.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  },
};

// Default story for Storybook controls
export const Default: Story = {
  args: {
    multiSelect: false,
    placeholder: 'Select an option...',
  },
  render: (args) => {
    const [value, setValue] = useState<string | string[]>(args.multiSelect ? [] : '');
    
    return (
      <div className="w-[280px]">
        <Select 
          {...args}
          value={value} 
          onValueChange={setValue}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Options</SelectLabel>
              {frameworks.map((framework) => (
                <SelectItem key={framework.value} value={framework.value}>
                  {framework.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    );
  },
};
