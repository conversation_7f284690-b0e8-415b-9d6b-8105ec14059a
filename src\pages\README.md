# Pages

This directory contains the main page components for the Migranium application.

## Available Pages

### Dashboard (`/`)
- **File**: `src/pages/Dashboard.tsx`
- **Route**: `/`
- **Description**: Main dashboard with overview statistics, quick actions, and feature highlights
- **Features**:
  - Quick stats cards
  - Feature announcements
  - Recent activity feed
  - Quick action buttons

### Clients (`/clients`)
- **File**: `src/pages/Clients.tsx`
- **Route**: `/clients`
- **Description**: Clients management page featuring the enhanced Select component demo
- **Features**:
  - Enhanced Select component demonstration
  - Single and multi-select examples
  - Component feature overview
  - Implementation guide with code examples
  - Interactive demos for testing functionality

## Enhanced Select Component Demo

The Clients page serves as a showcase for the new enhanced Select component that supports both single and multi-selection functionality. This provides users with:

### Interactive Examples
1. **Single Select**: Traditional select behavior with improved styling
2. **Multi Select**: Badge-based multi-selection with removal functionality
3. **Limited Multi Select**: Demonstrates selection limits and validation
4. **Code Examples**: Copy-paste ready implementation examples

### Component Features Demonstrated
- ✅ Backward compatibility with existing single-select usage
- ✅ Multi-select mode with badge display
- ✅ Selection limits and validation
- ✅ Removable badges with X buttons
- ✅ TypeScript support and type safety
- ✅ Accessibility features
- ✅ Responsive design

## Navigation Integration

The Clients page is fully integrated into the application's navigation system:

1. **Sidebar Navigation**: Available in the main sidebar with a dedicated icon
2. **Routing**: Configured in `src/main.tsx` with React Router
3. **Constants**: Route defined in `src/lib/utils/constants.ts`
4. **Permissions**: Ready for permission-based access control

## Usage

### Accessing the Clients Page
- Click on "Clients" in the sidebar navigation
- Navigate directly to `/clients` URL
- Use the "View Demo" button on the Dashboard

### Testing the Select Component
1. Navigate to the Clients page
2. Interact with the different Select component examples
3. Try single selection, multi-selection, and limited selection modes
4. Test badge removal functionality
5. Copy code examples for your own implementation

## File Structure

```
src/pages/
├── Dashboard.tsx     # Main dashboard page
├── Clients.tsx       # Clients page with Select demo
├── index.ts          # Page exports
└── README.md         # This documentation
```

## Implementation Details

### Routing Configuration
The pages are configured in `src/main.tsx` using React Router:

```tsx
const router = createBrowserRouter([
  {
    path: "/",
    element: <MainLayout />,
    children: [
      {
        path: "/",
        element: <Dashboard />,
      },
      {
        path: "/clients",
        element: <Clients />,
      },
    ],
  },
]);
```

### Layout Integration
Both pages use the `MainLayout` component which provides:
- Consistent sidebar navigation
- Header with user controls
- Responsive design
- Mobile-friendly drawer navigation

### Component Dependencies
The pages use the following UI components:
- `Card` components for layout structure
- `Badge` components for status indicators
- `Button` components for actions
- `Separator` for visual separation
- Enhanced `Select` component (demonstrated in Clients page)

## Future Enhancements

Potential improvements for the pages:
1. Add more interactive examples to the Clients page
2. Implement real client data management
3. Add form validation examples
4. Include more component demonstrations
5. Add search and filtering capabilities

## Development Notes

- All pages follow the established design patterns
- TypeScript is used throughout for type safety
- Components are imported from the centralized UI library
- Responsive design is implemented using Tailwind CSS
- Accessibility considerations are built into all components
