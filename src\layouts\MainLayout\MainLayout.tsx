import { Outlet } from "react-router";
import { Sidebar } from "../components/Sidebar";
import { Header } from "../components/Header";
import { useUIStore } from "@/stores/uiStore";

export const MainLayout = () => {
	const { mobileMenuOpen, setSidebarCollapsed, setMobileMenuOpen } =
		useUIStore();

	const handleSidebarClose = () => {
		// Close mobile menu on mobile, or collapse sidebar on desktop
		const isMobile = window.innerWidth < 768;
		if (isMobile) {
			setMobileMenuOpen(false);
		} else {
			setSidebarCollapsed(true);
		}
	};

	// On mobile, show drawer when mobileMenuOpen is true
	// On desktop, always show but respect collapsed state
	const isSidebarOpen =
		typeof window !== "undefined" && window.innerWidth < 768
			? mobileMenuOpen
			: true;

	return (
		<div className="flex h-screen bg-gray-100">
			<Sidebar isOpen={isSidebarOpen} onClose={handleSidebarClose} />

			<div className="flex flex-1 flex-col overflow-hidden">
				<Header />

				{/* Main content */}
				<main className="flex-1 overflow-y-auto p-6">
					<Outlet />
				</main>
			</div>
		</div>
	);
};
