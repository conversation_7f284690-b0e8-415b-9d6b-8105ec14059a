import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./styles/index.css";
// import App from './App.tsx'
import { createBrowserRouter, RouterProvider } from "react-router";
import { MainLayout } from "./layouts/MainLayout";
// import Customers from './pages/Customers';
import Clients from "./pages/Clients";

const router = createBrowserRouter([
	{
		path: "/",
		element: <MainLayout />,
		loader: async () => {
			// Load user permissions here
			// return { permissions: await getUserPermissions() };
		},
		children: [
			{
				path: "/clients",
				element: <Clients />,
				loader: async () => {
					// Clients page data loading
					// return { clientsData: await getClientsData() };
				},
			},
			// {
			//   path: "/customers",
			//   element: <Customers />,
			//   loader: async ({ request }) => {
			//     // Check permissions before loading
			//     const user = await getCurrentUser();
			//     if (!user.permissions.includes('customers.read')) {
			//       throw new Response("Unauthorized", { status: 401 });
			//     },
			//     return { customers: await getCustomers() };
			//   },
			// },
			// {
			//   path: "/analytics",
			//   element: <Analytics />,
			//   loader: async ({ request }) => {
			//     const user = await getCurrentUser();
			//     if (!user.permissions.includes('analytics.read')) {
			//       throw new Response("Unauthorized", { status: 401 });
			//     },
			//     return { analytics: await getAnalytics() };
			//   },
			// },
		],
	},
]);

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<RouterProvider router={router} />
	</StrictMode>
);
