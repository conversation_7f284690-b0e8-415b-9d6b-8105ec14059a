# Enhanced Select Component

A reusable Select component built on top of Radix UI that supports both single and multi-selection functionality while maintaining the shadcn/ui design patterns.

## Features

- ✅ **Single Selection** - Traditional select behavior
- ✅ **Multi Selection** - Select multiple options with badge display
- ✅ **Maximum Selection Limit** - Prevent selection beyond specified limit
- ✅ **Badge Display** - Selected items shown as removable badges
- ✅ **Responsive Design** - Works across different screen sizes
- ✅ **Accessibility** - Full keyboard navigation and screen reader support
- ✅ **TypeScript Support** - Complete type safety
- ✅ **Consistent API** - Follows shadcn/ui patterns

## Installation

The component is already included in your UI components. Make sure you have the required dependencies:

```bash
npm install @radix-ui/react-select lucide-react
```

## Basic Usage

### Single Select

```tsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

function SingleSelectExample() {
  const [value, setValue] = useState<string>("");

  return (
    <Select value={value} onValueChange={setValue}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select a fruit" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="apple">Apple</SelectItem>
        <SelectItem value="banana">Banana</SelectItem>
        <SelectItem value="orange">Orange</SelectItem>
      </SelectContent>
    </Select>
  );
}
```

### Multi Select

```tsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

function MultiSelectExample() {
  const [values, setValues] = useState<string[]>([]);

  return (
    <Select 
      multiSelect 
      value={values} 
      onValueChange={setValues}
      placeholder="Select multiple options"
    >
      <SelectTrigger className="w-[300px]">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="option1">Option 1</SelectItem>
        <SelectItem value="option2">Option 2</SelectItem>
        <SelectItem value="option3">Option 3</SelectItem>
      </SelectContent>
    </Select>
  );
}
```

### Multi Select with Limit

```tsx
function LimitedMultiSelectExample() {
  const [values, setValues] = useState<string[]>([]);

  return (
    <Select 
      multiSelect 
      value={values} 
      onValueChange={setValues}
      maxSelected={3}
      placeholder="Select up to 3 options"
    >
      <SelectTrigger className="w-[300px]">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="option1">Option 1</SelectItem>
        <SelectItem value="option2">Option 2</SelectItem>
        <SelectItem value="option3">Option 3</SelectItem>
        <SelectItem value="option4">Option 4</SelectItem>
      </SelectContent>
    </Select>
  );
}
```

## API Reference

### Select Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `multiSelect` | `boolean` | `false` | Enable multi-selection mode |
| `value` | `string \| string[]` | - | Current selected value(s) |
| `onValueChange` | `(value: string \| string[]) => void` | - | Callback when selection changes |
| `placeholder` | `string` | - | Placeholder text when no items selected |
| `maxSelected` | `number` | - | Maximum number of items (multi-select only) |

### SelectValue Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `showSelectedCount` | `boolean` | `true` | Show count when many items selected |
| `maxDisplayed` | `number` | `3` | Max badges to show before count |
| `placeholder` | `string` | - | Placeholder text override |

### SelectItem Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | - | **Required** - Item value |
| `children` | `ReactNode` | - | Item display content |

## Styling

The component uses the same CSS classes as the original shadcn/ui Select component, with additional classes for multi-select features:

- Badge styling follows the `Badge` component patterns
- Multi-select items use hover states for better UX
- Selected items show check icons consistently

## Examples

See the following files for complete examples:
- `src/components/ui/select.stories.tsx` - Storybook stories
- `src/components/ui/select-demo.tsx` - Interactive demo component

## Migration from Original Select

The enhanced Select component is backward compatible. Existing single-select usage will work without changes:

```tsx
// This continues to work exactly as before
<Select value={value} onValueChange={setValue}>
  <SelectTrigger>
    <SelectValue placeholder="Select..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="item1">Item 1</SelectItem>
  </SelectContent>
</Select>
```

To enable multi-select, simply add the `multiSelect` prop and update your state:

```tsx
// Add multiSelect prop and change state type
<Select multiSelect value={values} onValueChange={setValues}>
  {/* Same content structure */}
</Select>
```

## Accessibility

- Full keyboard navigation support
- Screen reader announcements for selection changes
- ARIA attributes for multi-select state
- Focus management for badge removal
- High contrast mode support

## Browser Support

Same as Radix UI Select:
- Chrome 51+
- Firefox 54+
- Safari 10+
- Edge 79+
