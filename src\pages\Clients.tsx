import * as React from "react";
import { SelectDemo } from "@/components/ui/select-demo";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/seperator";

export default function Clients() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Clients</h1>
            <p className="text-muted-foreground">
              Manage your client relationships and showcase enhanced UI components
            </p>
          </div>
          <Badge variant="secondary" className="text-xs">
            Component Demo
          </Badge>
        </div>
      </div>

      <Separator />

      {/* Enhanced Select Component Demo Section */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Enhanced Select Component Demo
              <Badge variant="outline" className="text-xs">
                New Feature
              </Badge>
            </CardTitle>
            <CardDescription>
              Explore the enhanced Select component that supports both single and multi-selection functionality.
              This component maintains backward compatibility while adding powerful new features for complex form interactions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SelectDemo />
          </CardContent>
        </Card>

        {/* Additional Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>Component Features</CardTitle>
            <CardDescription>
              Key capabilities of the enhanced Select component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-medium">Single Selection</h4>
                <p className="text-sm text-muted-foreground">
                  Traditional select behavior with improved styling and accessibility
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Multi Selection</h4>
                <p className="text-sm text-muted-foreground">
                  Select multiple options with badge display and removal functionality
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Selection Limits</h4>
                <p className="text-sm text-muted-foreground">
                  Configure maximum number of selections to prevent overflow
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Backward Compatible</h4>
                <p className="text-sm text-muted-foreground">
                  Existing single-select implementations continue to work unchanged
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">TypeScript Support</h4>
                <p className="text-sm text-muted-foreground">
                  Full type safety with proper interfaces and generic support
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Accessible</h4>
                <p className="text-sm text-muted-foreground">
                  Built with accessibility in mind, supporting keyboard navigation
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Guide Card */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Implementation Guide</CardTitle>
            <CardDescription>
              How to use the enhanced Select component in your forms
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Import the Component</h4>
                <div className="bg-muted p-3 rounded-md">
                  <code className="text-sm">
                    {`import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";`}
                  </code>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Single Select Usage</h4>
                <div className="bg-muted p-3 rounded-md">
                  <pre className="text-sm overflow-x-auto">
{`<Select value={value} onValueChange={setValue}>
  <SelectTrigger>
    <SelectValue placeholder="Choose option..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
  </SelectContent>
</Select>`}
                  </pre>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Multi Select Usage</h4>
                <div className="bg-muted p-3 rounded-md">
                  <pre className="text-sm overflow-x-auto">
{`<Select multiSelect value={values} onValueChange={setValues}>
  <SelectTrigger>
    <SelectValue placeholder="Choose multiple..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
  </SelectContent>
</Select>`}
                  </pre>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
