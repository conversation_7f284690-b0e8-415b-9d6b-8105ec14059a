import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';

// Mock the Badge component
vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className, ...props }: any) => (
    <span className={className} {...props}>
      {children}
    </span>
  ),
}));

describe('Select Component', () => {
  it('renders single select correctly', () => {
    const mockOnChange = vi.fn();
    
    render(
      <Select value="" onValueChange={mockOnChange}>
        <SelectTrigger>
          <SelectValue placeholder="Select option" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
        </SelectContent>
      </Select>
    );

    expect(screen.getByText('Select option')).toBeInTheDocument();
  });

  it('renders multi-select correctly', () => {
    const mockOnChange = vi.fn();
    
    render(
      <Select multiSelect value={[]} onValueChange={mockOnChange}>
        <SelectTrigger>
          <SelectValue placeholder="Select multiple" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
        </SelectContent>
      </Select>
    );

    expect(screen.getByText('Select multiple')).toBeInTheDocument();
  });

  it('displays selected values as badges in multi-select mode', () => {
    const mockOnChange = vi.fn();
    
    render(
      <Select multiSelect value={['option1', 'option2']} onValueChange={mockOnChange}>
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
        </SelectContent>
      </Select>
    );

    expect(screen.getByText('option1')).toBeInTheDocument();
    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('shows count when many items are selected', () => {
    const mockOnChange = vi.fn();
    const manyValues = ['opt1', 'opt2', 'opt3', 'opt4', 'opt5'];
    
    render(
      <Select multiSelect value={manyValues} onValueChange={mockOnChange}>
        <SelectTrigger>
          <SelectValue maxDisplayed={2} />
        </SelectTrigger>
        <SelectContent>
          {manyValues.map(value => (
            <SelectItem key={value} value={value}>
              {value}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );

    expect(screen.getByText('5 selected')).toBeInTheDocument();
  });

  it('respects maxSelected limit', () => {
    const mockOnChange = vi.fn();
    const maxSelected = 2;
    
    render(
      <Select 
        multiSelect 
        value={['option1', 'option2']} 
        onValueChange={mockOnChange}
        maxSelected={maxSelected}
      >
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
          <SelectItem value="option3">Option 3</SelectItem>
        </SelectContent>
      </Select>
    );

    // Component should render without errors
    expect(screen.getByText('option1')).toBeInTheDocument();
    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('handles single select value changes', () => {
    const mockOnChange = vi.fn();
    
    render(
      <Select value="option1" onValueChange={mockOnChange}>
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
        </SelectContent>
      </Select>
    );

    // In a real test, you would simulate clicking on the trigger and selecting an item
    // This is a basic structure test
    expect(mockOnChange).toHaveBeenCalledTimes(0);
  });

  it('handles multi-select value changes', () => {
    const mockOnChange = vi.fn();
    
    render(
      <Select multiSelect value={['option1']} onValueChange={mockOnChange}>
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
        </SelectContent>
      </Select>
    );

    expect(screen.getByText('option1')).toBeInTheDocument();
  });
});
