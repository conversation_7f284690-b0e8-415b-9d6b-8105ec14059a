import * as React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "./select";

// Example usage component demonstrating both single and multi-select
export function SelectDemo() {
  const [singleValue, setSingleValue] = React.useState<string>("");
  const [multiValues, setMultiValues] = React.useState<string[]>([]);
  const [limitedValues, setLimitedValues] = React.useState<string[]>([]);

  return (
    <div className="space-y-8 p-6 max-w-2xl mx-auto">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Select Component Demo</h2>
        <p className="text-muted-foreground">
          Enhanced Select component supporting both single and multi-selection modes.
        </p>
      </div>

      {/* Single Select Example */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Single Select</h3>
          <p className="text-sm text-muted-foreground">
            Traditional select behavior - choose one fruit
          </p>
        </div>
        <Select value={singleValue} onValueChange={setSingleValue}>
          <SelectTrigger className="w-[280px]">
            <SelectValue placeholder="Select a fruit" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Fruits</SelectLabel>
              <SelectItem value="apple">Apple</SelectItem>
              <SelectItem value="banana">Banana</SelectItem>
              <SelectItem value="blueberry">Blueberry</SelectItem>
              <SelectItem value="grapes">Grapes</SelectItem>
              <SelectItem value="pineapple">Pineapple</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
        {singleValue && (
          <p className="text-sm text-muted-foreground">
            Selected: <span className="font-medium capitalize">{singleValue}</span>
          </p>
        )}
      </div>

      {/* Multi Select Example */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Multi Select</h3>
          <p className="text-sm text-muted-foreground">
            Select multiple programming languages - displays as badges
          </p>
        </div>
        <Select 
          multiSelect 
          value={multiValues} 
          onValueChange={setMultiValues}
          placeholder="Select programming languages"
        >
          <SelectTrigger className="w-[400px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Programming Languages</SelectLabel>
              <SelectItem value="javascript">JavaScript</SelectItem>
              <SelectItem value="typescript">TypeScript</SelectItem>
              <SelectItem value="python">Python</SelectItem>
              <SelectItem value="java">Java</SelectItem>
              <SelectItem value="csharp">C#</SelectItem>
              <SelectItem value="go">Go</SelectItem>
              <SelectItem value="rust">Rust</SelectItem>
              <SelectItem value="php">PHP</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
        {multiValues.length > 0 && (
          <div className="text-sm text-muted-foreground">
            <p>Selected languages ({multiValues.length}):</p>
            <div className="flex flex-wrap gap-1 mt-1">
              {multiValues.map(value => (
                <span key={value} className="bg-secondary text-secondary-foreground px-2 py-1 rounded text-xs">
                  {value}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Multi Select with Limit */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Multi Select with Limit</h3>
          <p className="text-sm text-muted-foreground">
            Select up to 3 countries - prevents selection beyond limit
          </p>
        </div>
        <Select 
          multiSelect 
          value={limitedValues} 
          onValueChange={setLimitedValues}
          maxSelected={3}
          placeholder="Select up to 3 countries"
        >
          <SelectTrigger className="w-[350px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Countries (Max 3)</SelectLabel>
              <SelectItem value="us">United States</SelectItem>
              <SelectItem value="ca">Canada</SelectItem>
              <SelectItem value="uk">United Kingdom</SelectItem>
              <SelectItem value="de">Germany</SelectItem>
              <SelectItem value="fr">France</SelectItem>
              <SelectItem value="jp">Japan</SelectItem>
              <SelectItem value="au">Australia</SelectItem>
              <SelectItem value="br">Brazil</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
        <div className="text-sm text-muted-foreground">
          <p>{limitedValues.length}/3 countries selected</p>
          {limitedValues.length >= 3 && (
            <p className="text-amber-600 font-medium">Maximum selection reached</p>
          )}
        </div>
      </div>

      {/* Code Examples */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Usage Examples</h3>
        <div className="space-y-4">
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Single Select</h4>
            <pre className="text-sm overflow-x-auto">
{`<Select value={value} onValueChange={setValue}>
  <SelectTrigger>
    <SelectValue placeholder="Select option..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>`}
            </pre>
          </div>
          
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Multi Select</h4>
            <pre className="text-sm overflow-x-auto">
{`<Select 
  multiSelect 
  value={values} 
  onValueChange={setValues}
  maxSelected={5}
>
  <SelectTrigger>
    <SelectValue placeholder="Select multiple..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
